<?php
/**
 * 菜单调试页面
 * 访问: yoursite.com/wp-content/themes/hcylsoftcn/menu-debug.php
 */

// 加载 WordPress
require_once('../../../wp-load.php');

// 检查是否是管理员
if (!current_user_can('manage_options')) {
    wp_die('您没有权限访问此页面');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>菜单调试信息</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .debug-section h3 { margin-top: 0; color: #333; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>导航菜单调试信息</h1>
    
    <div class="debug-section">
        <h3>1. 主题菜单位置注册状态</h3>
        <?php
        $registered_menus = get_registered_nav_menus();
        if (!empty($registered_menus)) {
            echo '<p class="success">✓ 菜单位置已注册</p>';
            echo '<table>';
            echo '<tr><th>位置标识</th><th>显示名称</th></tr>';
            foreach ($registered_menus as $location => $description) {
                echo '<tr><td>' . $location . '</td><td>' . $description . '</td></tr>';
            }
            echo '</table>';
        } else {
            echo '<p class="error">✗ 没有注册的菜单位置</p>';
        }
        ?>
    </div>

    <div class="debug-section">
        <h3>2. 菜单分配状态</h3>
        <?php
        $menu_locations = get_nav_menu_locations();
        if (!empty($menu_locations)) {
            echo '<p class="success">✓ 有菜单分配到位置</p>';
            echo '<table>';
            echo '<tr><th>位置</th><th>菜单ID</th><th>菜单名称</th><th>状态</th></tr>';
            foreach ($menu_locations as $location => $menu_id) {
                $menu = wp_get_nav_menu_object($menu_id);
                $menu_name = $menu ? $menu->name : '未找到菜单';
                $status = $menu ? '<span class="success">正常</span>' : '<span class="error">菜单不存在</span>';
                echo '<tr><td>' . $location . '</td><td>' . $menu_id . '</td><td>' . $menu_name . '</td><td>' . $status . '</td></tr>';
            }
            echo '</table>';
        } else {
            echo '<p class="error">✗ 没有菜单分配到任何位置</p>';
        }
        ?>
    </div>

    <div class="debug-section">
        <h3>3. 主菜单 ('menu' 位置) 检查</h3>
        <?php
        if (has_nav_menu('menu')) {
            echo '<p class="success">✓ 主菜单位置有菜单分配</p>';
            
            // 获取菜单项
            $menu_items = wp_get_nav_menu_items(get_nav_menu_locations()['menu']);
            if ($menu_items) {
                echo '<p>菜单项数量: ' . count($menu_items) . '</p>';
                echo '<table>';
                echo '<tr><th>标题</th><th>URL</th><th>父级</th><th>状态</th></tr>';
                foreach ($menu_items as $item) {
                    $parent = $item->menu_item_parent ? '子菜单' : '主菜单';
                    $status = $item->post_status == 'publish' ? '<span class="success">发布</span>' : '<span class="warning">' . $item->post_status . '</span>';
                    echo '<tr><td>' . $item->title . '</td><td>' . $item->url . '</td><td>' . $parent . '</td><td>' . $status . '</td></tr>';
                }
                echo '</table>';
            } else {
                echo '<p class="warning">⚠ 菜单存在但没有菜单项</p>';
            }
        } else {
            echo '<p class="error">✗ 主菜单位置没有分配菜单</p>';
            echo '<p><strong>解决方案:</strong></p>';
            echo '<ol>';
            echo '<li>进入 WordPress 后台 → 外观 → 菜单</li>';
            echo '<li>创建一个新菜单或选择现有菜单</li>';
            echo '<li>在"菜单设置"中勾选"Primary"位置</li>';
            echo '<li>保存菜单</li>';
            echo '</ol>';
        }
        ?>
    </div>

    <div class="debug-section">
        <h3>4. 所有可用菜单</h3>
        <?php
        $menus = wp_get_nav_menus();
        if (!empty($menus)) {
            echo '<table>';
            echo '<tr><th>菜单名称</th><th>菜单ID</th><th>项目数量</th><th>操作</th></tr>';
            foreach ($menus as $menu) {
                $menu_items = wp_get_nav_menu_items($menu->term_id);
                $item_count = $menu_items ? count($menu_items) : 0;
                $assign_url = admin_url('nav-menus.php?action=locations&menu=' . $menu->term_id);
                echo '<tr>';
                echo '<td>' . $menu->name . '</td>';
                echo '<td>' . $menu->term_id . '</td>';
                echo '<td>' . $item_count . '</td>';
                echo '<td><a href="' . $assign_url . '" target="_blank">分配到位置</a></td>';
                echo '</tr>';
            }
            echo '</table>';
        } else {
            echo '<p class="error">✗ 没有创建任何菜单</p>';
            echo '<p><a href="' . admin_url('nav-menus.php') . '" target="_blank">创建菜单</a></p>';
        }
        ?>
    </div>

    <div class="debug-section">
        <h3>5. 主题文件检查</h3>
        <?php
        $theme_dir = get_template_directory();
        $files_to_check = [
            'header.php' => '头部文件',
            'functions.php' => '功能文件',
            'inc/class-hcyl-walker-nav-menu.php' => '菜单Walker类',
            'assets/css/responsive-header.css' => '响应式头部样式',
            'assets/css/menu-fix.css' => '菜单修复样式',
            'assets/js/menu-fix.js' => '菜单修复脚本'
        ];
        
        echo '<table>';
        echo '<tr><th>文件</th><th>描述</th><th>状态</th></tr>';
        foreach ($files_to_check as $file => $description) {
            $file_path = $theme_dir . '/' . $file;
            $exists = file_exists($file_path);
            $status = $exists ? '<span class="success">存在</span>' : '<span class="error">缺失</span>';
            echo '<tr><td>' . $file . '</td><td>' . $description . '</td><td>' . $status . '</td></tr>';
        }
        echo '</table>';
        ?>
    </div>

    <div class="debug-section">
        <h3>6. CSS 和 JS 加载检查</h3>
        <?php
        global $wp_styles, $wp_scripts;
        
        echo '<h4>已加载的样式文件:</h4>';
        if (!empty($wp_styles->registered)) {
            $menu_related_styles = array_filter($wp_styles->registered, function($style) {
                return strpos($style->handle, 'menu') !== false || 
                       strpos($style->handle, 'header') !== false ||
                       strpos($style->handle, 'responsive') !== false;
            });
            
            if (!empty($menu_related_styles)) {
                echo '<ul>';
                foreach ($menu_related_styles as $style) {
                    echo '<li>' . $style->handle . ' - ' . $style->src . '</li>';
                }
                echo '</ul>';
            } else {
                echo '<p class="warning">没有找到菜单相关的样式文件</p>';
            }
        }
        
        echo '<h4>已加载的脚本文件:</h4>';
        if (!empty($wp_scripts->registered)) {
            $menu_related_scripts = array_filter($wp_scripts->registered, function($script) {
                return strpos($script->handle, 'menu') !== false || 
                       strpos($script->handle, 'navigation') !== false ||
                       strpos($script->handle, 'responsive') !== false;
            });
            
            if (!empty($menu_related_scripts)) {
                echo '<ul>';
                foreach ($menu_related_scripts as $script) {
                    echo '<li>' . $script->handle . ' - ' . $script->src . '</li>';
                }
                echo '</ul>';
            } else {
                echo '<p class="warning">没有找到菜单相关的脚本文件</p>';
            }
        }
        ?>
    </div>

    <div class="debug-section">
        <h3>7. 快速修复建议</h3>
        <ol>
            <li><strong>如果菜单完全不显示:</strong>
                <ul>
                    <li>检查是否有菜单分配到 'menu' 位置</li>
                    <li>检查 CSS 是否隐藏了菜单</li>
                    <li>查看浏览器开发者工具的控制台是否有 JavaScript 错误</li>
                </ul>
            </li>
            <li><strong>如果菜单显示但样式异常:</strong>
                <ul>
                    <li>检查 CSS 文件是否正确加载</li>
                    <li>查看是否有 CSS 冲突</li>
                </ul>
            </li>
            <li><strong>如果移动端菜单不工作:</strong>
                <ul>
                    <li>检查 JavaScript 文件是否正确加载</li>
                    <li>查看浏览器控制台是否有错误</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="debug-section">
        <h3>8. 测试链接</h3>
        <p>
            <a href="<?php echo home_url(); ?>" target="_blank">查看网站首页</a> |
            <a href="<?php echo admin_url('nav-menus.php'); ?>" target="_blank">管理菜单</a> |
            <a href="<?php echo admin_url('customize.php'); ?>" target="_blank">主题定制</a>
        </p>
    </div>

    <script>
        // 在控制台输出调试信息
        console.log('=== 菜单调试信息 ===');
        console.log('当前页面:', window.location.href);
        console.log('用户代理:', navigator.userAgent);
        console.log('屏幕宽度:', window.innerWidth);
        
        // 检查菜单元素
        setTimeout(function() {
            const navMenu = document.querySelector('.nav-menu');
            const menuToggle = document.querySelector('.menu-toggle');
            console.log('导航菜单元素:', navMenu);
            console.log('菜单切换按钮:', menuToggle);
            if (navMenu) {
                console.log('菜单项数量:', navMenu.querySelectorAll('li').length);
                console.log('菜单可见性:', window.getComputedStyle(navMenu).display);
            }
        }, 1000);
    </script>
</body>
</html>
