<?php

/**
 * hcylsoftcn functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package hcylsoftcn
 */

if (! defined('_S_VERSION')) {
	// Replace the version number of the theme on each release.
	define('_S_VERSION', '1.0.0');
}

/**
 * Sets up theme defaults and registers support for various WordPress features.
 *
 * Note that this function is hooked into the after_setup_theme hook, which
 * runs before the init hook. The init hook is too late for some features, such
 * as indicating support for post thumbnails.
 */
function hcylsoftcn_setup()
{
	/*
		* Make theme available for translation.
		* Translations can be filed in the /languages/ directory.
		* If you're building a theme based on hcylsoftcn, use a find and replace
		* to change 'hcylsoftcn' to the name of your theme in all the template files.
		*/
	load_theme_textdomain('hcylsoftcn', get_template_directory() . '/languages');

	// Add default posts and comments RSS feed links to head.
	// add_theme_support( 'automatic-feed-links' );

	/*
		* Let WordPress manage the document title.
		* By adding theme support, we declare that this theme does not use a
		* hard-coded <title> tag in the document head, and expect WordPress to
		* provide it for us.
		*/
	add_theme_support('title-tag');

	/*
		* Enable support for Post Thumbnails on posts and pages.
		*
		* @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
		*/
	add_theme_support('post-thumbnails');

	// This theme uses wp_nav_menu() in one location.
	register_nav_menus(
		array(
			'menu' => esc_html__('Primary', 'hcylsoftcn'),
			'menu1' => esc_html__('bottom', 'hcylsoftcn'),
		)
	);

	/*
		* Switch default core markup for search form, comment form, and comments
		* to output valid HTML5.
		*/
	add_theme_support(
		'html5',
		array(
			'search-form',
			'comment-form',
			'comment-list',
			'gallery',
			'caption',
			'style',
			'script',
		)
	);

	// Set up the WordPress core custom background feature.
	// add_theme_support(
	// 	'custom-background',
	// 	apply_filters(
	// 		'hcylsoftcn_custom_background_args',
	// 		array(
	// 			'default-color' => 'ffffff',
	// 			'default-image' => '',
	// 		)
	// 	)
	// );

	// Add theme support for selective refresh for widgets.
	add_theme_support('customize-selective-refresh-widgets');

	/**
	 * Add support for core custom logo.
	 *
	 * @link https://codex.wordpress.org/Theme_Logo
	 */
	add_theme_support(
		'custom-logo',
		array(
			'height'      => 63,
			'width'       => 155,
			'flex-width'  => true,
			'flex-height' => true,
			'unlink-homepage-logo' => true
		)
	);
}
add_action('after_setup_theme', 'hcylsoftcn_setup');


function my_custom_theme_modifications()
{
	// 获取当前主题的某个设置值
	$banner_1 = get_theme_mod('banner_1');
	$banner_1 = str_replace("https://hcylsoft.cn", "http://localhost4:8881", $banner_1);
	set_theme_mod('banner_1', $banner_1);

	$companyprofile_img = get_theme_mod('companyprofile_img');
	$companyprofile_img = str_replace("https://hcylsoft.cn", "http://localhost4:8881", $companyprofile_img);
	set_theme_mod('companyprofile_img', $companyprofile_img);

	// 设置新的值
	//    set_theme_mod('custom_setting', '新的值');

	// 也可以删除某个设置
	// remove_theme_mod('custom_setting');
}

// add_action('after_setup_theme', 'my_custom_theme_modifications');



/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function hcylsoftcn_content_width()
{
	$GLOBALS['content_width'] = apply_filters('hcylsoftcn_content_width', 640);
}
add_action('after_setup_theme', 'hcylsoftcn_content_width', 0);

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function hcylsoftcn_widgets_init()
{
	register_sidebar(
		array(
			'name'          => esc_html__('Sidebar', 'hcylsoftcn'),
			'id'            => 'sidebar-1',
			'description'   => esc_html__('Add widgets here.', 'hcylsoftcn'),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);

	register_sidebar(
		array(
			'id'            => 'primary',
			'name'          => __('Primary Sidebar'),
			'description'   => __('A short description of the sidebar.'),
			'before_widget' => '<div id="%1$s" class="widget %2$s">',
			'after_widget'  => '</div>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);
}
add_action('widgets_init', 'hcylsoftcn_widgets_init');

/**
 * Enqueue scripts and styles.
 */
function hcylsoftcn_scripts()
{
	wp_enqueue_style('hcylsoftcn-style', get_stylesheet_uri(), array(), _S_VERSION);

	wp_enqueue_style('base', get_stylesheet_directory_uri() . '/assets/css/base.css', array(), '1.0.0', 'all');
	wp_enqueue_style('grid', get_stylesheet_directory_uri() . '/assets/css/grid.css', array(), '1.0.0', 'all');
	wp_enqueue_style('public', get_stylesheet_directory_uri() . '/assets/css/public.css', array(), '1.0.0', 'all');
	wp_enqueue_style('responsive-header', get_stylesheet_directory_uri() . '/assets/css/responsive-header.css', array(), '1.0.0', 'all');
	wp_enqueue_style('menu-fix', get_stylesheet_directory_uri() . '/assets/css/menu-fix.css', array(), '1.0.0', 'all');
	wp_enqueue_style('footer', get_stylesheet_directory_uri() . '/assets/css/footer.css', array(), '1.0.0', 'all');
	wp_enqueue_style('global-responsive', get_stylesheet_directory_uri() . '/assets/css/global-responsive.css', array(), '1.0.0', 'all');

	if (is_home()) {
		wp_enqueue_style('merged-index', get_stylesheet_directory_uri() . '/assets/css/merged-index.css', array(), '1.0.0', 'all');
	}
	if (is_category('solution') || in_category('solution')) {
		wp_enqueue_style('solution', get_stylesheet_directory_uri() . '/assets/css/solution.css', array(), '1.0.0', 'all');
	}

	if (is_category('product') || in_category('product')) {
		wp_enqueue_style('product', get_stylesheet_directory_uri() . '/assets/css/product.css', array(), '1.0.0', 'all');
	}

	if (is_category('aboutus') || in_category('aboutus')) {
		wp_enqueue_style('aboutus', get_stylesheet_directory_uri() . '/assets/css/about.css', array(), '1.0.0', 'all');
	}

	if (is_page('contactus')) {
		wp_enqueue_style('contactus', get_stylesheet_directory_uri() . '/assets/css/contact.css', array(), '1.0.0', 'all');
	}

	if (is_category('online-recruit')) {
		wp_enqueue_style('online-recruit', get_stylesheet_directory_uri() . '/assets/css/onlineRecruitment.css', array(), '1.0.0', 'all');
	}

	wp_enqueue_style('swiper', get_stylesheet_directory_uri() . '/assets/css/swiper.css', array(), '1.0.0', 'all');
	wp_enqueue_script('hcylsoftcn-navigation', get_template_directory_uri() . '/js/navigation.js', array(), _S_VERSION, true);
	wp_enqueue_script('jquery001', get_template_directory_uri() . '/assets/js/jquery-1.11.0.min.js', array(), _S_VERSION, false);
	wp_enqueue_script('swiper', get_template_directory_uri() . '/assets/js/swiper.js', array(), _S_VERSION, false);
	wp_enqueue_script('rem', get_template_directory_uri() . '/assets/js/rem.js', array(), _S_VERSION, false);
	wp_enqueue_script('hcylsoft-common', get_template_directory_uri() . '/assets/js/common.js', array(), _S_VERSION, false);
	wp_enqueue_script('hcylsoft-mobile-menu', get_template_directory_uri() . '/assets/js/app/common.js', array('jquery001'), _S_VERSION, true);
	wp_enqueue_script('responsive-menu', get_template_directory_uri() . '/assets/js/responsive-menu.js', array(), _S_VERSION, true);
	wp_enqueue_script('menu-fix', get_template_directory_uri() . '/assets/js/menu-fix.js', array(), _S_VERSION, true);

	if (is_page()) {

		wp_enqueue_script('tailwindcss', get_template_directory_uri() . '/assets/js/tailwindcss.3.4.5.js', array(), _S_VERSION, false);
	}
}
add_action('wp_enqueue_scripts', 'hcylsoftcn_scripts');

/**
 * 获取当前category的标题
 */
function get_category_post_title()
{

	$cat_id = get_query_var('cat');
	$category = get_category($cat_id);
	$count = $category->count;
	$col = 12 / $count;
	$i = 0;
	query_posts(array(
		'cat' => $cat_id,
		'order' => 'ASC',
		'orderby' => 'ID'
	));
	while (have_posts()) {
		the_post();
?>
		<li class="col-md-<?= $col ?> col-sm-<?= $col ?>">
			<a href="javascript:;" <?php if ($i == 0): echo 'class="selected"';
									endif; ?>>
				<?php the_title() ?>
			</a>
		</li>
	<?php
		$i++;
	}
	wp_reset_query();
}
function get_category_post_title_swiper()
{

	$i = 0;
	while (have_posts()) {
		the_post();
	?>
		<ul class="swiper-slide">
			<li><a href="javascript:;" <?php if ($i == 0): echo 'class="active"';
										endif; ?>> <?php the_title() ?></a></li>
		</ul>
	<?php
		$i++;
	}
}


function get_category_about()
{

	?>
	<ul class="mb-tabs mb-only">
		<?php
		$cat_id = get_query_var('cat');
		$i = 0;
		query_posts(array(
			'cat' => $cat_id,
			'order' => 'ASC',
			'orderby' => 'ID'
		));
		while (have_posts()) {
			the_post();
		?>
			<li class="col-xs-3"><a href="javascript:;" <?php if ($i == 0): echo 'class="active"';
														endif; ?>> <?php the_title() ?></a></li>
		<?php
			$i++;
		}
		wp_reset_query();
		?>
	</ul>
<?php

}

function get_mobile_logo()
{

	$custom_logo_id = get_theme_mod('custom_logo');
	echo wp_get_attachment_image($custom_logo_id, array('110', '40'), "", "");
}

function banners()
{

	return $banners = array(
		array(
			'id' => 'banner_1',
			'title' => '轮播图1',
			'description' => '轮播图1'
		),
		array(
			'id' => 'banner_2',
			'title' => '轮播图2',
			'description' => '轮播图2'
		),
		array(
			'id' => 'banner_3',
			'title' => '轮播图3',
			'description' => '轮播图3'
		),
		array(
			'id' => 'banner_4',
			'title' => '轮播图4',
			'description' => '轮播图4'
		),
	);
}

//对于菜单链接进行过滤
function  hcyl_nav_menu_link_attributes($atts, $menu_item, $args, $depth)
{
	$locations = get_nav_menu_locations();
	if ($menu_item->object == 'category' && $args->theme_location == 'menu') {
		unset($atts['href']);
	}
	return $atts;
}
add_filter('nav_menu_link_attributes', 'hcyl_nav_menu_link_attributes', 10, 4);

//给特殊菜单增加new图标

function addMenuNewIcon($item_output, $menu_item, $depth, $args)
{
	$template_value = get_post_meta($menu_item->object_id, 'shownew', true);
	if ($template_value == 'yes' && $depth == 0) {
		$newIconUrl = get_stylesheet_directory_uri() . '/assets/img/common/new.gif';
		$item_output .= "<img class='new-icon'  src='$newIconUrl' />";
	}
	return $item_output;
}

add_filter('walker_nav_menu_start_el', 'addMenuNewIcon', 10, 4);


// 控制摘要长度
function limitContent($num)
{
	// 获取文章内容
	$content = get_the_excerpt();

	// 将内容限制在指定的长度
	$limited_excerpt = wp_trim_words($content, $num); // 这里的 100 是你想要显示的字符数

	// 输出内容
	return $limited_excerpt;
}

require get_template_directory() . '/inc/class-hcyl-walker-nav-menu.php';

/**
 * Implement the Custom Header feature.
 */
require get_template_directory() . '/inc/custom-header.php';

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Load Jetpack compatibility file.
 */
if (defined('JETPACK__VERSION')) {
	require get_template_directory() . '/inc/jetpack.php';
}
