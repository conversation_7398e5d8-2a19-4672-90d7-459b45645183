/* global wp, jQuery */
/**
 * File customizer.js.
 *
 * Theme Customizer enhancements for a better user experience.
 *
 * Contains handlers to make Theme Customizer preview reload changes asynchronously.
 */

( function( $ ) {
	// Site title and description.
	wp.customize( 'blogname', function( value ) {
		console.log('blogname',value)
		value.bind( function( to ) {
			$( '.site-title a' ).text( to );
		} );
	} );

	wp.customize( 'blogdescription', function( value ) {
		value.bind( function( to ) {
			$( '.site-description' ).text( to );
		} );
	} );

	wp.customize( 'companyprofile', function( value ) {
		value.bind( function( to ) {
			$( '.company-profile-content, .company-intro-content' ).text(to);
		} );
	} );

	// wp.customize( 'companyprofile_url', function( value ) {
	// 	value.bind( function( to ) {
	// 		$( '.company-profile-l a' ).attr('href', to);
	// 	} );
	// } );

	// wp.customize( 'bipmvideo', function( value ) {
	// 	debugger
	// 	value.bind( function( to ) {
	// 		console.log('to', to)
	// 		debugger
	// 		if(to == ''){
	// 			video.src = null;
	// 			// $( '.bipmvideo' ).attr( 'src', '' );
	// 		}else {
	// 			var attachment = new wp.media.model.Attachment({ id: to });
	// 			var media_url = attachment.get( 'url' );
	// 			// $( '.bipmvideo' ).attr( 'src', media_url );
	// 			video.src = media_url;
	// 		}
	//
	// 	} );
	// } );


	// Header text color.
	wp.customize( 'header_textcolor', function( value ) {
		value.bind( function( to ) {
			if ( 'blank' === to ) {
				$( '.site-title, .site-description' ).css( {
					clip: 'rect(1px, 1px, 1px, 1px)',
					position: 'absolute',
				} );
			} else {
				$( '.site-title, .site-description' ).css( {
					clip: 'auto',
					position: 'relative',
				} );
				$( '.site-title a, .site-description' ).css( {
					color: to,
				} );
			}
		} );
	} );
}( jQuery ) );
