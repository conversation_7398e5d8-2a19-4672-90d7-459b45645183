.about-bg-img {
  position: relative;
}

.wrapper-pad {
  /* padding-top: 90px; */
}

.about-img {
  width: 65%;
}

.about-img img {
  width: 100%;
}

.about-text {
  width: 210px;
  margin: 0 auto;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: 30%;
}
.about-company-content {
  text-align: justify;
}
.about-c {
  font-size: 46px;
  font-weight: bold;
  line-height: 81px;
  color: #fff;
  letter-spacing: 5px;
}

.about-e {
  font-size: 20px;
  line-height: 24px;
  color: #fff;
  text-align: center;
}

.about-tabs {
  position: absolute;
  bottom: 0px;
  left: 50%;
  margin-left: -605px;
  width: 1210px;
}

.about-tabs li a {
  background: #fff;
  display: inline-block;
  line-height: 58px;
  padding-left: 100px;
  padding-right: 100px;
  color: #333;
  font-size: 18px;
}

.selected {
  background: #4f7fe8 !important;
  color: #fff !important;
}

.about-company-box {
  background: #f8f8f8;
  padding-bottom: 80px;
}

.about-company {
  /* width: 1210px; */
  margin: 0 auto;
  position: relative;
  /* padding-top: 110px; */
  display: flex;
  align-items: flex-end;
  min-height: 400px;
}

.company-intro {
  width: 58%;
  background: #4f7fe8;
  padding: 64px 57px 75px 56px;
  color: #fff;
  line-height: 24px;
  margin-left: auto;
  box-sizing: border-box;
}

.company-intro-content {
  font-size: 14px;
  text-align: justify;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.company-msg-box {
  width: 30%;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.company-name {
  font-size: 32px;
  color: #333333;
  line-height: 50px;
}

.company-purpose {
  font-size: 24px;
  color: #4f7fe8;
  line-height: 24px;
}

.company-target {
  color: #333;
  font-size: 16px;
  line-height: 24px;
  margin-top: 45px;
}

.about-line {
  width: 106px;
  height: 3px;
  background: linear-gradient(90deg, #4f7fe8, #97b5f7);
  margin-top: 10px;
}

.company-business-box {
  width: 1210px;
  margin: 0 auto;
  padding-top: 50px;
  padding-bottom: 60px;
}

.company-business-title {
  position: relative;
  width: 50%;
  margin: 0 auto;
  text-align: center;
  height: 60px;
}

.business-title-e {
  color: rgba(79, 127, 232, 0.1);
  font-size: 36px;
  line-height: 37px;
  text-align: center;
}

.business-title-c {
  color: #333;
  font-size: 24px;
  line-height: 32px;
  text-align: center;
  position: absolute;
  bottom: 12px;
  left: 30%;
}

.business-img {
  width: 845px;
  margin: 0 auto;
}

.business-img img {
  width: 100%;
  max-width: 100%;
  margin-top: 45px;
}

.company-resources-box {
  width: 1210px;
  margin: 0 auto;
  overflow: hidden;
  margin-top: 30px;
  padding-bottom: 10px;
}

.company-resources-item {
  text-align: center;
  margin-bottom: 30px;
}

.resources-item-summary {
  font-size: 18px;
  color: #333;
  line-height: 24px;
}

.resources-item-detail {
  font-size: 14px;
  color: #333;
  line-height: 24px;
  padding-left: 30px;
  padding-right: 30px;
  margin-top: 20px;
  text-align: left;
}

.about-content-main {
  display: none;
}

.about-content-bg {
  background: #f8f8f8;
}

.dis-none {
  display: none;
}

.dis-block {
  display: block;
}

/*公司资质*/
.qualifications-title {
  width: 1210px;
  margin: 0 auto;
  padding-top: 35px;
}

.qualifications-title-c {
  font-size: 24px;
  line-height: 32px;
  color: #333;
}

.qualifications-title-e {
  color: #ccc;
  font-size: 24px;
  line-height: 32px;
  display: inline-block;
  /*margin-left: 10px;*/
}

.qualifications-line {
  display: inline-block;
  width: 42px;
  height: 4px;
  background: linear-gradient(90deg, #4f7fe8, #97b5f7);
}

.qualifications-certificate-box {
  overflow: hidden;
  width: 1210px;
  margin: 0 auto;
  margin-top: 30px;
  text-align: center;
  color: #333;
}

.qualifications-certificate-box p {
  margin-top: 10px;
  margin-bottom: 10px;
}

.swiper-container-qualifications {
  position: relative;
}

.swiper-container-qualifications {
  width: 1210px;
  margin: 0 auto;
  padding-bottom: 30px;
}

.swiper-container-qualifications .swiper-button-prev {
  left: -5% !important;
  transform: translateY(-50%);
  width: 52px;
  outline: none;
}

.swiper-container-qualifications .swiper-button-next {
  right: -5% !important;
  transform: translateY(-50%);
  width: 52px;
  outline: none;
}

.qualifications-img p {
  text-align: center;
  margin-top: 10px;
  margin-bottom: 10px;
}

.qualifications-img p img {
  margin-left: 5px;
}

.swiper-button-prev:after,
.swiper-container-rtl .swiper-button-next:after {
  content: "" !important;
}

.swiper-button-next:after,
.swiper-container-rtl .swiper-button-prev:after {
  content: "" !important;
}

/*发展历程*/
.cource-title-1 {
  font-size: 18px;
  color: #333;
  text-align: center;
  padding-top: 50px;
}

.cource-title-2 {
  font-size: 24px;
  color: #333;
  text-align: center;
  margin-top: 20px;
  margin-bottom: 10px;
}

.cource-img {
  width: 100%;
  position: relative;
}

.cource-img img {
  width: 100%;
  vertical-align: bottom;
}

.create-box {
  position: absolute;
  top: 65px;
  left: 0;
  right: 0;
  margin: auto;
}

.create-time {
  font-size: 28px;
  line-height: 48px;
  text-align: center;
  color: #4f7fe8;
}

.create-name {
  font-size: 20px;
  line-height: 36px;
  text-align: center;
  color: #4f7fe8;
}

.cource-content {
  width: 1210px;
  margin: 0 auto;
  min-height: 600px;
  position: relative;
  margin-bottom: 60px;
}

.cource-top {
  width: 100%;
}

.cource-top img {
  display: block;
  margin: 0 auto;
  margin-top: 30px;
  margin-left: calc(50% - 129px);
}

.cource-detail-box {
  min-height: 75px;
  padding-top: 100px;
  padding-bottom: 100px;
  font-size: 20px;
  color: #333;
  line-height: 30px;
  position: relative;
  margin-top: -5px;
  z-index: 100;
}

.cource-detail-box:nth-child(even) .detail-l {
  color: #fff;
}

.cource-detail-box:nth-child(odd) .detail-r {
  color: #fff;
}

.cource-ring {
  width: 30px;
  height: 30px;
  box-sizing: border-box;
  border: 1px solid #4c7adf;
  border-radius: 50%;
  position: absolute;
  left: calc(50% - 15px);
  top: 70%;
  z-index: 300;
}

.cource-circle {
  width: 20px;
  height: 20px;
  background: #4c7adf;
  border-radius: 50%;
  margin: 0 auto;
  margin-top: 4px;
}

.detail-l {
  padding-right: 60px;
  text-align: right;
  box-sizing: border-box;
}

.detail-r {
  padding-left: 60px;
  text-align: left;
  box-sizing: border-box;
}

.cource-detail-box::after {
  position: absolute;
  display: block;
  content: "";
  width: 1px;
  height: 100%;
  background: #4f7fe8;
  top: 0;
  left: 50%;
  z-index: 150;
}

.cource-detail-box:last-child {
  min-height: 0px;
}

.cource-detail-box:last-child .detail-l {
  color: #fff;
}

.cource-detail-box:last-child .detail-r {
  color: #fff;
}

.cource-detail-box:last-child::after {
  top: -36px;
}

/*核心团队*/
.team-intro {
  width: 1210px;
  margin: 0 auto;
  padding: 52px 58px 54px 57px;
  background: #fff;
  margin-top: 30px;
  font-size: 15px;
  line-height: 32px;
  box-sizing: border-box;
}

.team-img {
  width: 1210px;
  margin: 0 auto;
  overflow: hidden;
}

.team-box {
  padding: 60px 80px;
  background: #f9fbfd;
  height: 210px;
}

.team-title {
  font-size: 30px;
  color: #333;
  text-align: left;
  margin-top: 40px;
}

.team-content {
  background: #f9fbfd;
  font-size: 16px;
  color: #333;
  margin-top: 30px;
  text-align: left;
}

.team-line {
  width: 44px;
  height: 3px;
  background: linear-gradient(90deg, #97b5f7, #4f7fe8);
  margin-top: 10px;
}

.imgbox {
  height: 330px;
  overflow: hidden;
  margin-bottom: 10px;
  margin-right: 10px;
}

.imgbox img {
  width: 100%;
}

.cert-img-box {
  width: 49%;
}

.cert-img-box:nth-child(1) {
  float: left;
}

.cert-img-box:nth-child(2) {
  float: right;
}
